"use client";
import Image from "next/image";
import { useParallax } from "../hooks/useParallax";

export default function ResponsibilitySection() {
  const parallaxRef = useParallax(0.25);
  return (
    <section className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 xl:gap-20 items-center">
          {/* Image Content - First on mobile, second on desktop */}
          <div className="relative order-1 lg:order-2 overflow-hidden rounded-lg">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <div ref={parallaxRef} className="w-full h-[120%] -mt-[10%]">
                <Image
                  src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80"
                  alt="Polished Round Diamond"
                  width={600}
                  height={720}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          {/* Text Content - Second on mobile, first on desktop */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8 order-2 lg:order-1">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-light text-gray-900 leading-tight relative">
              <span className="block font-extralight tracking-wider">Responsibility</span>
              <span
                className="block font-bold tracking-wide text-4xl sm:text-5xl lg:text-6xl xl:text-7xl"
                style={{
                  background: 'linear-gradient(135deg, #059669 0%, #0891b2 50%, #0284c7 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Redefined
              </span>
              <div
                className="absolute -bottom-4 left-0 w-28 h-1 rounded-full"
                style={{
                  background: 'linear-gradient(135deg, #059669 0%, #0891b2 50%, #0284c7 100%)'
                }}
              ></div>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
              A sustainability company prioritises the well-being of its employees and respects the
              broader communities and natural environment it operates in. As an impulse for impact,
              Taché partners with CSR initiatives and local charities to steer our industry's
              commitment towards achieving ESG goals.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
