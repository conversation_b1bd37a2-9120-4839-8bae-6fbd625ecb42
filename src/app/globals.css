@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Smooth scrolling and transitions */
* {
  box-sizing: border-box;
}

/* Custom scrollbar hide */
::-webkit-scrollbar {
  display: none;
}

/* Image split animation improvements */
.image-split-left {
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

.image-split-right {
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}

/* Smooth transitions for all elements */
.transition-smooth {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Background gradients for sections */
.bg-gradient-quality {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.bg-gradient-solutions {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.bg-gradient-innovation {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
}

.bg-gradient-responsibility {
  background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%);
}

/* Custom animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Stylish DA Logo Effects */
@keyframes glow-pulse {
  0%, 100% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5),
                 0 0 10px rgba(255, 255, 255, 0.3),
                 0 0 15px rgba(255, 255, 255, 0.2);
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
                 0 0 20px rgba(255, 255, 255, 0.5),
                 0 0 30px rgba(255, 255, 255, 0.3);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.logo-glow {
  animation: glow-pulse 3s ease-in-out infinite;
}

.logo-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}
