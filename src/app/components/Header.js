"use client";
import { useState } from "react";
import Link from "next/link";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSustainabilityOpen, setIsSustainabilityOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="flex justify-between items-center h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 hover:text-gray-700 transition-colors">
              Group Taché
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:block">
            <div className="ml-10 flex items-baseline space-x-6 xl:space-x-8">
              <Link href="#" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium transition-colors">
                Solutions
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium transition-colors">
                Departments
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium transition-colors">
                About
              </Link>
              
              {/* Sustainability Dropdown */}
              <div className="relative">
                <button
                  onClick={() => setIsSustainabilityOpen(!isSustainabilityOpen)}
                  className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium flex items-center transition-colors"
                >
                  Sustainability
                  <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isSustainabilityOpen && (
                  <div className="absolute top-full left-0 mt-1 w-80 xl:w-96 bg-white rounded-md shadow-lg py-1 z-50 border">
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-gray-700 hover:bg-gray-100 transition-colors">
                      Happify Our Employees
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-gray-700 hover:bg-gray-100 transition-colors">
                      Empower Communities & Encourage Change
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-gray-700 hover:bg-gray-100 transition-colors">
                      Protect the World Around Us
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-gray-700 hover:bg-gray-100 transition-colors">
                      Comply with Certifications & Standards
                    </Link>
                  </div>
                )}
              </div>

              <Link href="#" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium transition-colors">
                Highlights
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm lg:text-base font-medium transition-colors">
                Online inventory
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t shadow-lg">
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                Solutions
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                Departments
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                About
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                Sustainability
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                Highlights
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 hover:bg-gray-50 block px-3 py-3 text-base font-medium rounded-md transition-colors">
                Online inventory
              </Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
