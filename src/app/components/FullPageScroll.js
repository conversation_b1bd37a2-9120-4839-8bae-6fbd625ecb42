"use client";
import { useEffect, useRef, useState, useCallback } from "react";
import Image from "next/image";
import <PERSON><PERSON> from "lottie-react";

const sections = [
  {
    id: "quality",
    title: "A Never-ending Quest for Quality",
    description: "At Taché, quality is not just a characteristic; it is the cornerstone of everything we do. With precision, determination and dedication, we select nature's purest treasures and enrich them with years of expertise. The outcome? Exceptional diamonds for our cherished clientele. Because they deserve nothing less.",
    image: "/image/tache_diamond_rough_cut.png",
    bgColor: "bg-gradient-quality"
  },
  {
    id: "solutions",
    title: "Providers of Solutions",
    description: "Through years of forging intrinsic and enduring relationships with our clients, we have learned to cater for their needs and understand their DNA. We offer unwavering reliability through a set of bespoke and tailor-made solutions.",
    image: "/image/laser_phase_tache.png",
    bgColor: "bg-gradient-solutions"
  },
  {
    id: "innovation",
    title: "Our Mission is Innovation",
    description: "Through proprietary innovation, research and cutting-edge technologies, we have shattered norms, redefined reliability and infused transparency into the diamond supply chain.",
    image: "/image/shaping_phase_tache-768x768.png",
    bgColor: "bg-gradient-innovation"
  },
  {
    id: "responsibility",
    title: "Responsibility Redefined",
    description: "A sustainability company prioritises the well-being of its employees and respects the broader communities and natural environment it operates in. As an impulse for impact, Taché partners with CSR initiatives and local charities to steer our industry's commitment towards achieving ESG goals.",
    image: "/image/download.png",
    bgColor: "bg-gradient-responsibility"
  }
];

// Hook to get window dimensions
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export default function FullPageScroll() {
  const containerRef = useRef(null);
  const lottieRef = useRef(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [imageSplitProgress, setImageSplitProgress] = useState({});
  const [isImageSplitting, setIsImageSplitting] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showLottieAnimation, setShowLottieAnimation] = useState(false);
  const [currentAnimation, setCurrentAnimation] = useState(null);


  // Touch handling with refs
  const touchStartRef = useRef(null);
  const touchEndRef = useRef(null);

  // Load animation data
  useEffect(() => {
    const loadAnimations = async () => {
      try {
        const response1 = await fetch('/animations/tache_transition_pt1.json');
        const animation1 = await response1.json();
        // Store animation data for later use
        window.transitionPt1 = animation1;

        const response2 = await fetch('/animations/tache_transition_pt2.json');
        const animation2 = await response2.json();
        window.transitionPt2 = animation2;
      } catch (error) {
        console.log('Animation files not found, using CSS animations instead');
      }
    };

    loadAnimations();
  }, []);
  const windowSize = useWindowSize();

  const handleNavigation = useCallback((direction) => {
    if (isScrolling) return;

    setIsScrolling(true);

    if (direction === 'up' && currentSection > 0) {
      const targetSection = currentSection - 1;

      // Reset target section's image split progress
      setImageSplitProgress(prev => ({
        ...prev,
        [targetSection]: 0
      }));

      // Move to previous section
      setCurrentSection(targetSection);

      setTimeout(() => {
        setIsScrolling(false);
      }, 800);
    }
      else if (direction === 'down' && currentSection < sections.length - 1) {
        const currentProgress = imageSplitProgress[currentSection] || 0;
        
        // If image not split yet, split it first
        if (currentProgress === 0) {
          setIsImageSplitting(true);

          // Show Lottie animation
          if (window.transitionPt1) {
            setShowLottieAnimation(true);
            setCurrentAnimation(window.transitionPt1);
            if (lottieRef.current) {
              lottieRef.current.play();
            }
          }

          // Animate image split
          const splitDuration = 600;
          const startTime = Date.now();

          const animateSplit = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / splitDuration, 1);

            setImageSplitProgress(prev => ({
              ...prev,
              [currentSection]: progress
            }));

            if (progress < 1) {
              requestAnimationFrame(animateSplit);
            } else {
              setShowLottieAnimation(false);
              setIsImageSplitting(false);
              
              // Move to next section after split
              setTimeout(() => {
                setCurrentSection(prev => prev + 1);
                setIsScrolling(false);
              }, 200);
            }
          };

          requestAnimationFrame(animateSplit);
        } else {
          // Image already split, just move to next section
          setCurrentSection(prev => prev + 1);
          setTimeout(() => {
            setIsScrolling(false);
          }, 800);
        }
      } else {
        // At boundaries, just reset scrolling
        setIsScrolling(false);
      }
  }, [currentSection, isScrolling, imageSplitProgress]);

  useEffect(() => {
    let scrollTimeout;
    let splitTimeout;

    const handleWheel = (e) => {
      e.preventDefault();
      const direction = e.deltaY > 0 ? 'down' : 'up';
      handleNavigation(direction);
    };

    const handleKeyDown = (e) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        handleNavigation('down');
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        handleNavigation('up');
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }

    const handleTouchStart = (e) => {
      touchEndRef.current = null;
      touchStartRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchMove = (e) => {
      touchEndRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchEnd = () => {
      if (!touchStartRef.current || !touchEndRef.current) return;
      const distance = touchStartRef.current - touchEndRef.current;
      const isUpSwipe = distance > 50;
      const isDownSwipe = distance < -50;

      if (isUpSwipe) {
        handleNavigation('down');
      } else if (isDownSwipe) {
        handleNavigation('up');
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    if (container) {
      container.addEventListener('touchstart', handleTouchStart);
      container.addEventListener('touchmove', handleTouchMove);
      container.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('touchend', handleTouchEnd);
      }
      document.removeEventListener('keydown', handleKeyDown);
      clearTimeout(scrollTimeout);
      clearTimeout(splitTimeout);
    };
  }, [handleNavigation]);

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative"
    >
      {/* Lottie Animation Overlay */}
      {showLottieAnimation && currentAnimation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <Lottie
            ref={lottieRef}
            animationData={currentAnimation}
            loop={false}
            autoplay={true}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      )}
      
      {/* Scroll Indicators */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-30 space-y-4">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full border-2 border-white transition-all duration-300 ${
              index === currentSection ? 'bg-white' : 'bg-transparent'
            }`}
          />
        ))}
      </div>
      
      {/* Scroll Hint */}
      {currentSection === 0 && (imageSplitProgress[0] || 0) === 0 && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-30 text-white text-center animate-bounce">
          <div className="text-sm mb-2">Scroll to explore</div>
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      )}
      
      {sections.map((section, index) => (
        <div
          key={section.id}
          className={`absolute inset-0 transition-transform duration-1000 ease-in-out ${section.bgColor}`}
          style={{
            transform: `translateY(${(index - currentSection) * 100}%)`,
            zIndex: index === currentSection ? 10 : 1
          }}
        >
          {/* Center Image - Complete Circle that Splits on Scroll */}
          <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
            <div className="relative w-80 h-80 lg:w-96 lg:h-96">

            {/* Complete Image - Shows when current section and not splitting */}
            <div
              className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
              style={{
                opacity: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 1 : 0,
                transform: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 'translate(0, 0) scale(1)' : 'translate(0, 0) scale(0.8)',
                transformOrigin: 'center center'
              }}
            >
              <Image
                src={section.image}
                alt={section.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
            </div>

            {/* Left Half - Goes to Bottom Left Corner */}
            <div
              className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
              style={{
                clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)',
                opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                transform: (() => {
                  const progress = imageSplitProgress[index] || 0;
                  if (progress > 0) {
                    return `translate(${-progress * (windowSize.width/2 + 100)}px, ${progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`;
                  } else {
                    return 'translate(0, 0) scale(1)';
                  }
                })(),
                transformOrigin: 'center center'
              }}
            >
              <Image
                src={section.image}
                alt={section.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
            </div>

            {/* Right Half - Goes to Top Right Corner */}
            <div
              className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
              style={{
                clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)',
                opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                transform: (() => {
                  const progress = imageSplitProgress[index] || 0;
                  return progress > 0
                    ? `translate(${progress * (windowSize.width/2 + 100)}px, ${-progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`
                    : 'translate(0, 0) scale(1)';
                })(),
                transformOrigin: 'center center'
              }}
            >
              <Image
                src={section.image}
                alt={section.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
            </div>

            </div>
          </div>
          
          {/* Text Content - Positioned around center image */}
          <div className="absolute inset-0 flex flex-col items-center justify-center px-8 lg:px-16">
            {/* Title above image */}
            <div className="text-center mb-8 lg:mb-12 max-w-4xl">
              <h1 className="text-3xl lg:text-5xl font-light leading-tight text-white">
                {section.title}
              </h1>
            </div>
            
            {/* Space for center image */}
            <div className="w-80 h-80 lg:w-96 lg:h-96 mb-8 lg:mb-12"></div>
            
            {/* Description below image */}
            <div className="text-center max-w-4xl">
              <p className="text-lg lg:text-xl text-gray-300 leading-relaxed font-light">
                {section.description}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
