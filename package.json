{"name": "da_new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "lottie-react": "^2.4.1", "mongoose": "^8.16.5", "next": "15.4.4", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4"}}