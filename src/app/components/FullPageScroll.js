"use client";
import { useEffect, useRef, useState, useCallback } from "react";
import Image from "next/image";
import <PERSON><PERSON> from "lottie-react";

const sections = [
  {
    id: "quality",
    title: "A Never-ending Quest for Quality",
    description: "At Taché, quality is not just a characteristic; it is the cornerstone of everything we do. With precision, determination and dedication, we select nature's purest treasures and enrich them with years of expertise. The outcome? Exceptional diamonds for our cherished clientele. Because they deserve nothing less.",
    image: "/image/tache_diamond_rough_cut.png",
    bgColor: "bg-gradient-quality"
  },
  {
    id: "solutions",
    title: "Providers of Solutions",
    description: "Through years of forging intrinsic and enduring relationships with our clients, we have learned to cater for their needs and understand their DNA. We offer unwavering reliability through a set of bespoke and tailor-made solutions.",
    image: "/image/laser_phase_tache.png",
    bgColor: "bg-gradient-solutions"
  },
  {
    id: "innovation",
    title: "Our Mission is Innovation",
    description: "Through proprietary innovation, research and cutting-edge technologies, we have shattered norms, redefined reliability and infused transparency into the diamond supply chain.",
    image: "/image/shaping_phase_tache-768x768.png",
    bgColor: "bg-gradient-innovation"
  },
  {
    id: "responsibility",
    title: "Responsibility Redefined",
    description: "A sustainability company prioritises the well-being of its employees and respects the broader communities and natural environment it operates in. As an impulse for impact, Taché partners with CSR initiatives and local charities to steer our industry's commitment towards achieving ESG goals.",
    image: "/image/download.png",
    bgColor: "bg-gradient-responsibility"
  },
  {
    id: "footer",
    title: null,
    description: null,
    image: null,
    bgColor: "bg-gray-900",
    isFooter: true
  }
];

// Hook to get window dimensions
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export default function FullPageScroll() {
  const containerRef = useRef(null);
  const lottieRef = useRef(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [imageSplitProgress, setImageSplitProgress] = useState({});
  const [isImageSplitting, setIsImageSplitting] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showLottieAnimation, setShowLottieAnimation] = useState(false);
  const [currentAnimation, setCurrentAnimation] = useState(null);



  // Touch handling with refs
  const touchStartRef = useRef(null);
  const touchEndRef = useRef(null);

  // Load animation data
  useEffect(() => {
    const loadAnimations = async () => {
      try {
        const response1 = await fetch('/animations/tache_transition_pt1.json');
        const animation1 = await response1.json();
        // Store animation data for later use
        window.transitionPt1 = animation1;

        const response2 = await fetch('/animations/tache_transition_pt2.json');
        const animation2 = await response2.json();
        window.transitionPt2 = animation2;
      } catch (error) {
        console.log('Animation files not found, using CSS animations instead');
      }
    };

    loadAnimations();
  }, []);
  const windowSize = useWindowSize();

  const handleNavigation = useCallback((direction) => {
    if (isScrolling) return;

    setIsScrolling(true);

    if (direction === 'up' && currentSection > 0) {
      const targetSection = currentSection - 1;

      // Reset target section's image split progress
      setImageSplitProgress(prev => ({
        ...prev,
        [targetSection]: 0
      }));

      // Move to previous section
      setCurrentSection(targetSection);

      setTimeout(() => {
        setIsScrolling(false);
      }, 800);
    }
      else if (direction === 'down' && currentSection < sections.length - 1) {
        const currentProgress = imageSplitProgress[currentSection] || 0;
        
        // If image not split yet, split it first
        if (currentProgress === 0) {
          setIsImageSplitting(true);

          // Show Lottie animation
          if (window.transitionPt1) {
            setShowLottieAnimation(true);
            setCurrentAnimation(window.transitionPt1);
            if (lottieRef.current) {
              lottieRef.current.play();
            }
          }

          // Animate image split
          const splitDuration = 600;
          const startTime = Date.now();

          const animateSplit = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / splitDuration, 1);

            setImageSplitProgress(prev => ({
              ...prev,
              [currentSection]: progress
            }));

            if (progress < 1) {
              requestAnimationFrame(animateSplit);
            } else {
              setShowLottieAnimation(false);
              setIsImageSplitting(false);
              
              // Move to next section after split
              setTimeout(() => {
                setCurrentSection(prev => prev + 1);
                setIsScrolling(false);
              }, 200);
            }
          };

          requestAnimationFrame(animateSplit);
        } else {
          // Image already split, just move to next section
          setCurrentSection(prev => prev + 1);
          setTimeout(() => {
            setIsScrolling(false);
          }, 800);
        }
      } else {
        // At boundaries, just reset scrolling
        setIsScrolling(false);
      }
  }, [currentSection, isScrolling, imageSplitProgress]);

  useEffect(() => {
    let scrollTimeout;
    let splitTimeout;

    const handleWheel = (e) => {
      e.preventDefault();
      const direction = e.deltaY > 0 ? 'down' : 'up';
      handleNavigation(direction);
    };

    const handleKeyDown = (e) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        handleNavigation('down');
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        handleNavigation('up');
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }

    const handleTouchStart = (e) => {
      touchEndRef.current = null;
      touchStartRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchMove = (e) => {
      touchEndRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchEnd = () => {
      if (!touchStartRef.current || !touchEndRef.current) return;
      const distance = touchStartRef.current - touchEndRef.current;
      const isUpSwipe = distance > 50;
      const isDownSwipe = distance < -50;

      if (isUpSwipe) {
        handleNavigation('down');
      } else if (isDownSwipe) {
        handleNavigation('up');
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    if (container) {
      container.addEventListener('touchstart', handleTouchStart);
      container.addEventListener('touchmove', handleTouchMove);
      container.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('touchend', handleTouchEnd);
      }
      document.removeEventListener('keydown', handleKeyDown);
      clearTimeout(scrollTimeout);
      clearTimeout(splitTimeout);
    };
  }, [handleNavigation]);

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative z-10"
      style={{ height: '100vh' }}
    >
      {/* Lottie Animation Overlay */}
      {showLottieAnimation && currentAnimation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <Lottie
            ref={lottieRef}
            animationData={currentAnimation}
            loop={false}
            autoplay={true}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      )}
      
      {/* Scroll Indicators */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-30 space-y-4">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full border-2 border-white transition-all duration-300 ${
              index === currentSection ? 'bg-white' : 'bg-transparent'
            }`}
          />
        ))}
      </div>
      
      {/* Scroll Hint */}
      {currentSection === 0 && (imageSplitProgress[0] || 0) === 0 && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-30 text-white text-center animate-bounce">
          <div className="text-sm mb-2">Scroll to explore</div>
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      )}
      
      {sections.map((section, index) => (
        <div
          key={section.id}
          className={`absolute inset-0 transition-transform duration-1000 ease-in-out ${section.bgColor}`}
          style={{
            transform: `translateY(${(index - currentSection) * 100}%)`,
            zIndex: index === currentSection ? 10 : 1
          }}
        >
          {/* Render Footer Section */}
          {section.isFooter ? (
            <div className="absolute inset-0 flex flex-col items-center justify-center px-8 lg:px-16 relative overflow-hidden">
              {/* Animated Background Elements */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-r from-pink-400 to-blue-500 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full blur-2xl animate-pulse delay-500"></div>
              </div>

              {/* Floating Particles */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-1/4 left-1/3 w-1 h-1 bg-white rounded-full animate-ping delay-300"></div>
                <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-white rounded-full animate-ping delay-700"></div>
                <div className="absolute bottom-1/3 left-1/5 w-1 h-1 bg-white rounded-full animate-ping delay-1000"></div>
                <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-white rounded-full animate-ping delay-1500"></div>
              </div>

              {/* Footer Content */}
              <div className="max-w-6xl mx-auto text-center relative z-10">
                {/* Logo with Enhanced Effects */}
                <div className="flex items-center justify-center mb-16 group">
                  <div className="flex flex-col items-center mr-6 relative">
                    <div className="w-4 h-4 bg-white rounded-full mb-2 shadow-lg shadow-white/50"></div>
                    <div className="w-4 h-4 bg-white rounded-full mb-2 shadow-lg shadow-white/50"></div>
                    <div className="w-4 h-4 bg-white rounded-full shadow-lg shadow-white/50"></div>
                  </div>
                  <span className="text-6xl lg:text-8xl font-black text-white tracking-[0.3em] relative">
                    DA
                  </span>
                </div>

                {/* Main Footer Text */}
                <div className="mb-16 relative">
                  <h2 className="text-4xl lg:text-6xl font-light text-white mb-8 relative">
                    Get in Touch
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-full mt-4"></div>
                  </h2>
                  <p className="text-xl lg:text-2xl text-gray-300 leading-relaxed max-w-4xl mx-auto font-light">
                    Ready to discover exceptional diamonds? Contact us to explore our collection and experience the
                    <span className="text-white font-semibold"> DA difference</span>.
                  </p>
                </div>

                {/* Contact Info Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-12 lg:gap-16 mb-16">
                  {/* Address */}
                  <div className="group text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-105 transition-all duration-300 shadow-2xl">
                      <svg className="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <h3 className="text-white font-bold text-xl mb-4">Visit Us</h3>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      123 Diamond District<br />
                      New York, NY 10001
                    </p>
                  </div>

                  {/* Phone */}
                  <div className="group text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-105 transition-all duration-300 shadow-2xl">
                      <svg className="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <h3 className="text-white font-bold text-xl mb-4">Call Us</h3>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      +****************
                    </p>
                  </div>

                  {/* Email */}
                  <div className="group text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-pink-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-105 transition-all duration-300 shadow-2xl">
                      <svg className="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-white font-bold text-xl mb-4">Email Us</h3>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <EMAIL>
                    </p>
                  </div>
                </div>

                {/* Enhanced Social Links */}
                <div className="flex justify-center space-x-8 mb-12">
                  <a href="#" className="group relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-2xl shadow-blue-500/30">
                      <svg className="h-7 w-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                      </svg>
                      {/* Glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
                    </div>
                  </a>
                  <a href="#" className="group relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-2xl shadow-purple-500/30">
                      <svg className="h-7 w-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                      {/* Glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
                    </div>
                  </a>
                  <a href="#" className="group relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-blue-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-2xl shadow-pink-500/30">
                      <svg className="h-7 w-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.90-5.367 11.99-11.987C24.007 5.367 18.641.001.012.001z"/>
                      </svg>
                      {/* Glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-blue-600 rounded-2xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
                    </div>
                  </a>
                  <a href="#" className="group relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-2xl shadow-green-500/30">
                      <svg className="h-7 w-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001.012.001z"/>
                      </svg>
                      {/* Glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
                    </div>
                  </a>
                </div>

                {/* Enhanced Copyright */}
                <div className="relative">
                  <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-500 to-transparent"></div>
                  <div className="pt-8">
                    <p className="text-gray-400 text-base lg:text-lg font-light">
                      © 2024 <span className="text-transparent bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text font-medium">DA</span>. All rights reserved.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Center Image - Complete Circle that Splits on Scroll */}
              <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                <div className="relative w-80 h-80 lg:w-96 lg:h-96">

                {/* Complete Image - Shows when current section and not splitting */}
                <div
                  className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                  style={{
                    opacity: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 1 : 0,
                    transform: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 'translate(0, 0) scale(1)' : 'translate(0, 0) scale(0.8)',
                    transformOrigin: 'center center'
                  }}
                >
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover"
                    priority={index === 0}
                  />
                </div>

            {/* Left Half - Goes to Bottom Left Corner */}
            <div
              className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
              style={{
                clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)',
                opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                transform: (() => {
                  const progress = imageSplitProgress[index] || 0;
                  if (progress > 0) {
                    return `translate(${-progress * (windowSize.width/2 + 100)}px, ${progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`;
                  } else {
                    return 'translate(0, 0) scale(1)';
                  }
                })(),
                transformOrigin: 'center center'
              }}
            >
              <Image
                src={section.image}
                alt={section.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
            </div>

            {/* Right Half - Goes to Top Right Corner */}
            <div
              className="absolute w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
              style={{
                clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)',
                opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                transform: (() => {
                  const progress = imageSplitProgress[index] || 0;
                  return progress > 0
                    ? `translate(${progress * (windowSize.width/2 + 100)}px, ${-progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`
                    : 'translate(0, 0) scale(1)';
                })(),
                transformOrigin: 'center center'
              }}
            >
              <Image
                src={section.image}
                alt={section.title}
                fill
                className="object-cover"
                priority={index === 0}
              />
            </div>

                </div>
              </div>

              {/* Text Content - Positioned around center image */}
              <div className="absolute inset-0 flex flex-col items-center justify-center px-8 lg:px-16">
                {/* Title above image */}
                <div className="text-center mb-8 lg:mb-12 max-w-4xl">
                  <h1 className="text-3xl lg:text-5xl font-light leading-tight text-white">
                    {section.title}
                  </h1>
                </div>

                {/* Space for center image */}
                <div className="w-80 h-80 lg:w-96 lg:h-96 mb-8 lg:mb-12"></div>

                {/* Description below image */}
                <div className="text-center max-w-4xl">
                  <p className="text-lg lg:text-xl text-gray-300 leading-relaxed font-light">
                    {section.description}
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      ))}
    </div>
  );
}
