"use client";
import Image from "next/image";
import { useParallax } from "../hooks/useParallax";

export default function InnovationSection() {
  const parallaxRef = useParallax(0.35);
  return (
    <section className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 xl:gap-20 items-center">
          {/* Text Content */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-light text-gray-900 leading-tight relative">
              <span className="block font-light">Our Mission is</span>
              <span className="block text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text font-semibold tracking-wide">
                Innovation
              </span>
              <div className="absolute -bottom-4 left-0 w-20 h-1 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 rounded-full"></div>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
              Through proprietary innovation, research and cutting-edge technologies, we have shattered
              norms, redefined reliability and infused transparency into the diamond supply chain.
            </p>
          </div>

          {/* Image Content */}
          <div className="relative overflow-hidden rounded-lg">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <div ref={parallaxRef} className="w-full h-[120%] -mt-[10%]">
                <Image
                  src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80"
                  alt="Diamond Shaping Phase"
                  width={600}
                  height={720}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
