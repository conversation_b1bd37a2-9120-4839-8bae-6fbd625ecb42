"use client";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSustainabilityOpen, setIsSustainabilityOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle screen size changes
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth >= 1024) {
        setIsMenuOpen(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMenuOpen && !event.target.closest('nav')) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMenuOpen]);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-transparent backdrop-blur-sm">
      <nav className="max-w-8xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div className="flex justify-between items-center h-14 sm:h-16 md:h-18 lg:h-20 xl:h-22">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            {/* Logo Icon - Three circles stacked - Larger sizes */}
            <div className="flex flex-col items-center mr-3 sm:mr-4">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 md:w-3 md:h-3 bg-white rounded-full mb-1"></div>
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 md:w-3 md:h-3 bg-white rounded-full mb-1"></div>
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 md:w-3 md:h-3 bg-white rounded-full"></div>
            </div>
            <Link href="/" className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white hover:text-gray-300 transition-colors duration-300 tracking-wider sm:tracking-widest">
              DA
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:block">
            <div className="flex items-center space-x-4 lg:space-x-6 xl:space-x-8 2xl:space-x-10">
              <Link href="#" className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium transition-colors uppercase tracking-wide">
                SOLUTIONS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium transition-colors uppercase tracking-wide">
                DEPARTMENTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium transition-colors uppercase tracking-wide">
                ABOUT
              </Link>

              {/* Sustainability Dropdown */}
              <div className="relative">
                <button
                  onClick={() => setIsSustainabilityOpen(!isSustainabilityOpen)}
                  className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium flex items-center transition-colors uppercase tracking-wide"
                >
                  SUSTAINABILITY
                  <svg className="ml-1 h-3 w-3 lg:h-4 lg:w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isSustainabilityOpen && (
                  <div className="absolute top-full left-0 mt-1 w-72 lg:w-80 xl:w-96 bg-gray-800 bg-opacity-95 backdrop-blur-sm rounded-md shadow-lg py-1 z-50 border border-gray-600">
                    <Link href="#" className="block px-3 lg:px-4 py-2 lg:py-3 text-xs lg:text-sm xl:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Happify Our Employees
                    </Link>
                    <Link href="#" className="block px-3 lg:px-4 py-2 lg:py-3 text-xs lg:text-sm xl:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Empower Communities & Encourage Change
                    </Link>
                    <Link href="#" className="block px-3 lg:px-4 py-2 lg:py-3 text-xs lg:text-sm xl:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Protect the World Around Us
                    </Link>
                    <Link href="#" className="block px-3 lg:px-4 py-2 lg:py-3 text-xs lg:text-sm xl:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Comply with Certifications & Standards
                    </Link>
                  </div>
                )}
              </div>

              <Link href="#" className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium transition-colors uppercase tracking-wide">
                HIGHLIGHTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-2 lg:px-3 py-2 text-xs lg:text-sm xl:text-base font-medium transition-colors uppercase tracking-wide">
                ONLINE INVENTORY
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-1.5 sm:p-2 rounded-md text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 transition-colors"
            >
              <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-gray-900 bg-opacity-98 backdrop-blur-md border-t border-gray-600 shadow-2xl">
            <div className="px-3 sm:px-4 pt-2 pb-3 space-y-1">
              <Link
                href="#"
                onClick={() => setIsMenuOpen(false)}
                className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-sm sm:text-base font-medium rounded-md transition-colors uppercase tracking-wide"
              >
                SOLUTIONS
              </Link>
              <Link
                href="#"
                onClick={() => setIsMenuOpen(false)}
                className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-sm sm:text-base font-medium rounded-md transition-colors uppercase tracking-wide"
              >
                DEPARTMENTS
              </Link>
              <Link
                href="#"
                onClick={() => setIsMenuOpen(false)}
                className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-sm sm:text-base font-medium rounded-md transition-colors uppercase tracking-wide"
              >
                ABOUT
              </Link>

              {/* Mobile Sustainability Section */}
              <div className="border-t border-gray-700 pt-2 mt-2">
                <div className="text-gray-400 px-3 py-2 text-xs uppercase tracking-wider font-semibold">
                  Sustainability
                </div>
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-6 py-2 text-sm font-medium rounded-md transition-colors"
                >
                  Happify Our Employees
                </Link>
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-6 py-2 text-sm font-medium rounded-md transition-colors"
                >
                  Empower Communities & Encourage Change
                </Link>
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-6 py-2 text-sm font-medium rounded-md transition-colors"
                >
                  Protect the World Around Us
                </Link>
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-6 py-2 text-sm font-medium rounded-md transition-colors"
                >
                  Comply with Certifications & Standards
                </Link>
              </div>

              <div className="border-t border-gray-700 pt-2 mt-2">
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-sm sm:text-base font-medium rounded-md transition-colors uppercase tracking-wide"
                >
                  HIGHLIGHTS
                </Link>
                <Link
                  href="#"
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-sm sm:text-base font-medium rounded-md transition-colors uppercase tracking-wide"
                >
                  ONLINE INVENTORY
                </Link>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
