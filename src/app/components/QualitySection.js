"use client";
import { useEffect, useRef, useState } from "react";

export default function QualitySection() {
  const sectionRef = useRef(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const rect = sectionRef.current.getBoundingClientRect();
        const sectionTop = rect.top;
        const sectionHeight = rect.height;
        const windowHeight = window.innerHeight;

        // Calculate scroll progress when section is in view
        if (sectionTop < windowHeight && sectionTop > -sectionHeight) {
          const progress = Math.max(0, Math.min(1, (windowHeight - sectionTop) / (windowHeight + sectionHeight)));
          setScrollProgress(progress);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section ref={sectionRef} className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-gray-900 text-white relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 xl:gap-20 items-center">
          {/* Text Content */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8 z-10 relative">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-light text-white leading-tight mt-16 sm:mt-20 lg:mt-24 xl:mt-32 relative">
              <span className="block">A Never-ending</span>
              <span className="block text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text font-semibold tracking-wide">
                Quest for Quality
              </span>
              <div className="absolute -bottom-4 left-0 w-24 h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-full"></div>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-300 leading-relaxed max-w-2xl">
              At Taché, quality is not just a characteristic; it is the cornerstone of everything we do.
              With precision, determination and dedication, we select nature's purest treasures and enrich
              them with years of expertise. The outcome? Exceptional diamonds for our cherished clientele.
              Because they deserve nothing less.
            </p>
          </div>

          {/* Custom Circles Animation */}
          <div className="relative flex justify-center items-center h-96 lg:h-[500px] overflow-hidden">
            {/* Multiple animated circles */}
            <div className="relative w-80 h-80 lg:w-96 lg:h-96">
              {/* Circle 1 - Center */}
              <div
                className="absolute top-1/2 left-1/2 w-32 h-32 lg:w-40 lg:h-40 border-2 border-white rounded-full transition-all duration-1000 ease-out"
                style={{
                  transform: `translate(-50%, -50%) scale(${1 + scrollProgress * 0.5}) rotate(${scrollProgress * 180}deg)`,
                  opacity: 0.8 - scrollProgress * 0.3
                }}
              />

              {/* Circle 2 - Left */}
              <div
                className="absolute top-1/2 left-1/2 w-24 h-24 lg:w-32 lg:h-32 border border-white rounded-full transition-all duration-1000 ease-out"
                style={{
                  transform: `translate(-50%, -50%) translateX(${-scrollProgress * 120}px) scale(${1 + scrollProgress * 0.3}) rotate(${-scrollProgress * 90}deg)`,
                  opacity: 0.6 + scrollProgress * 0.4
                }}
              />

              {/* Circle 3 - Right */}
              <div
                className="absolute top-1/2 left-1/2 w-24 h-24 lg:w-32 lg:h-32 border border-white rounded-full transition-all duration-1000 ease-out"
                style={{
                  transform: `translate(-50%, -50%) translateX(${scrollProgress * 120}px) scale(${1 + scrollProgress * 0.3}) rotate(${scrollProgress * 90}deg)`,
                  opacity: 0.6 + scrollProgress * 0.4
                }}
              />

              {/* Circle 4 - Top */}
              <div
                className="absolute top-1/2 left-1/2 w-20 h-20 lg:w-24 lg:h-24 border border-white/70 rounded-full transition-all duration-1000 ease-out"
                style={{
                  transform: `translate(-50%, -50%) translateY(${-scrollProgress * 80}px) scale(${1 + scrollProgress * 0.2})`,
                  opacity: 0.4 + scrollProgress * 0.6
                }}
              />

              {/* Circle 5 - Bottom */}
              <div
                className="absolute top-1/2 left-1/2 w-20 h-20 lg:w-24 lg:h-24 border border-white/70 rounded-full transition-all duration-1000 ease-out"
                style={{
                  transform: `translate(-50%, -50%) translateY(${scrollProgress * 80}px) scale(${1 + scrollProgress * 0.2})`,
                  opacity: 0.4 + scrollProgress * 0.6
                }}
              />

              {/* Center glow effect */}
              <div
                className="absolute top-1/2 left-1/2 w-4 h-4 bg-white rounded-full transition-all duration-1000"
                style={{
                  transform: `translate(-50%, -50%) scale(${1 + scrollProgress * 3})`,
                  opacity: scrollProgress * 0.8,
                  boxShadow: `0 0 ${20 + scrollProgress * 40}px rgba(255, 255, 255, ${scrollProgress * 0.6})`
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20" />
      </div>
    </section>
  );
}
