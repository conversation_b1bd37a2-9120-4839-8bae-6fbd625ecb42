"use client";
import { useState } from "react";
import Link from "next/link";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSustainabilityOpen, setIsSustainabilityOpen] = useState(false);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-transparent backdrop-blur-sm">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="flex justify-between items-center h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            {/* Logo Icon - Three circles stacked */}
            <div className="flex flex-col items-center mr-3">
              <div className="w-2 h-2 bg-white rounded-full mb-1"></div>
              <div className="w-2 h-2 bg-white rounded-full mb-1"></div>
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <Link href="/" className="text-xl sm:text-2xl lg:text-3xl font-bold text-white hover:text-gray-300 transition-colors">
              Group Taché
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:block">
            <div className="flex items-center space-x-8 xl:space-x-10">
              <Link href="#" className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium transition-colors uppercase tracking-wide">
                SOLUTIONS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium transition-colors uppercase tracking-wide">
                DEPARTMENTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium transition-colors uppercase tracking-wide">
                ABOUT
              </Link>

              {/* Sustainability Dropdown */}
              <div className="relative">
                <button
                  onClick={() => setIsSustainabilityOpen(!isSustainabilityOpen)}
                  className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium flex items-center transition-colors uppercase tracking-wide"
                >
                  SUSTAINABILITY
                  <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isSustainabilityOpen && (
                  <div className="absolute top-full left-0 mt-1 w-80 xl:w-96 bg-gray-800 bg-opacity-95 backdrop-blur-sm rounded-md shadow-lg py-1 z-50 border border-gray-600">
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Happify Our Employees
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Empower Communities & Encourage Change
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Protect the World Around Us
                    </Link>
                    <Link href="#" className="block px-4 py-3 text-sm lg:text-base text-white hover:bg-gray-700 hover:bg-opacity-50 transition-colors">
                      Comply with Certifications & Standards
                    </Link>
                  </div>
                )}
              </div>

              <Link href="#" className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium transition-colors uppercase tracking-wide">
                HIGHLIGHTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 px-3 py-2 text-sm lg:text-base font-medium transition-colors uppercase tracking-wide">
                ONLINE INVENTORY
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 transition-colors"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800 bg-opacity-95 backdrop-blur-sm border-t border-gray-600 shadow-lg rounded-b-md">
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                SOLUTIONS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                DEPARTMENTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                ABOUT
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                SUSTAINABILITY
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                HIGHLIGHTS
              </Link>
              <Link href="#" className="text-white hover:text-gray-300 hover:bg-gray-700 hover:bg-opacity-50 block px-3 py-3 text-base font-medium rounded-md transition-colors uppercase tracking-wide">
                ONLINE INVENTORY
              </Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
