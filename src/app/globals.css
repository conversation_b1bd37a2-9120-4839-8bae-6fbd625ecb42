@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow: hidden;
}

/* Smooth scrolling and transitions */
* {
  box-sizing: border-box;
}

/* Custom scrollbar hide */
::-webkit-scrollbar {
  display: none;
}

/* Image split animation improvements */
.image-split-left {
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

.image-split-right {
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}

/* Smooth transitions for all elements */
.transition-smooth {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Background gradients for sections */
.bg-gradient-quality {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.bg-gradient-solutions {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.bg-gradient-innovation {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
}

.bg-gradient-responsibility {
  background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%);
}

/* Custom animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}
