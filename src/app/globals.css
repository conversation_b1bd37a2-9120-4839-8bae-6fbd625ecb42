@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Smooth scrolling and transitions */
* {
  box-sizing: border-box;
}

/* Custom scrollbar hide */
::-webkit-scrollbar {
  display: none;
}

/* Image split animation improvements */
.image-split-left {
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

.image-split-right {
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}

/* Smooth transitions for all elements */
.transition-smooth {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Background gradients for sections */
.bg-gradient-quality {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.bg-gradient-solutions {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.bg-gradient-innovation {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
}

.bg-gradient-responsibility {
  background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%);
}

/* Custom animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Elegant heading animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDiagonalTopLeft {
  from {
    opacity: 0;
    transform: translate(-50px, -50px);
  }
  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}

@keyframes slideInDiagonalBottomRight {
  from {
    opacity: 0;
    transform: translate(50px, 50px);
  }
  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}

@keyframes letterSpacing {
  0% {
    letter-spacing: 0.05em;
  }
  50% {
    letter-spacing: 0.15em;
  }
  100% {
    letter-spacing: 0.1em;
  }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
  }
  50% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3),
                 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 1s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 1s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 1s ease-out forwards;
}

.animate-slideInDown {
  animation: slideInDown 1s ease-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 1s ease-out forwards;
}

.animate-slideInDiagonalTopLeft {
  animation: slideInDiagonalTopLeft 1.2s ease-out forwards;
}

.animate-slideInDiagonalBottomRight {
  animation: slideInDiagonalBottomRight 1.2s ease-out forwards;
}

.animate-letterSpacing {
  animation: letterSpacing 3s ease-in-out infinite;
}

.animate-textGlow {
  animation: textGlow 4s ease-in-out infinite;
}

.heading-elegant {
  font-weight: 300;
  letter-spacing: 0.1em;
  line-height: 1.2;
  position: relative;
}

.heading-elegant::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 2px;
  background: white;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.8s ease-out;
}

.heading-elegant.animate-underline::after {
  transform: scaleX(1);
}

/* Stylish DA Logo Effects */
@keyframes glow-pulse {
  0%, 100% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5),
                 0 0 10px rgba(255, 255, 255, 0.3),
                 0 0 15px rgba(255, 255, 255, 0.2);
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
                 0 0 20px rgba(255, 255, 255, 0.5),
                 0 0 30px rgba(255, 255, 255, 0.3);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.logo-glow {
  animation: glow-pulse 3s ease-in-out infinite;
}

.logo-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}
