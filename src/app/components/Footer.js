import Link from "next/link";
import Image from "next/image";

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12 py-8 sm:py-12 lg:py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
          {/* Social Links */}
          <div className="flex justify-center md:justify-start space-x-4">
            <Link href="#" className="hover:opacity-80 hover:scale-110 transition-all duration-200">
              <div className="w-8 h-8 lg:w-10 lg:h-10 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-sm lg:text-base font-bold">Li</span>
              </div>
            </Link>
            <Link href="#" className="hover:opacity-80 hover:scale-110 transition-all duration-200">
              <div className="w-8 h-8 lg:w-10 lg:h-10 bg-pink-600 rounded flex items-center justify-center">
                <span className="text-white text-sm lg:text-base font-bold">Ig</span>
              </div>
            </Link>
          </div>

          {/* Copyright */}
          <div className="text-center">
            <p className="text-sm lg:text-base text-gray-300">
              © 2025 • Taché Company, all rights reserved
            </p>
          </div>

          {/* Contact Link */}
          <div className="flex justify-center md:justify-end">
            <Link href="#" className="text-sm lg:text-base text-gray-300 hover:text-white transition-colors">
              Contact us
            </Link>
          </div>
        </div>

        {/* Footer Links */}
        <div className="mt-6 sm:mt-8 lg:mt-12 pt-6 sm:pt-8 lg:pt-12 border-t border-gray-700">
          <div className="flex flex-wrap justify-center gap-4 sm:gap-6 lg:gap-8 text-sm lg:text-base">
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              Legal Notice
            </Link>
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              Personal Data
            </Link>
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              CSR Policy
            </Link>
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              Annual Review
            </Link>
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              Grievance Mechanism
            </Link>
            <Link href="#" className="text-gray-300 hover:text-white transition-colors">
              Terms Conditions
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
