"use client";
import Image from "next/image";
import { useParallax } from "../hooks/useParallax";

export default function SolutionsSection() {
  const parallaxRef = useParallax(0.4);
  return (
    <section className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 xl:gap-20 items-center">
          {/* Image Content - First on mobile, second on desktop */}
          <div className="relative order-1 lg:order-2 overflow-hidden rounded-lg">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <div ref={parallaxRef} className="w-full h-[120%] -mt-[10%]">
                <Image
                  src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80"
                  alt="Laser Phase Technology"
                  width={600}
                  height={720}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          {/* Text Content - Second on mobile, first on desktop */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8 order-2 lg:order-1">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-light text-gray-900 leading-tight relative">
              <span className="block font-extralight tracking-wider">Providers of</span>
              <span
                className="block font-bold tracking-wide text-4xl sm:text-5xl lg:text-6xl xl:text-7xl"
                style={{
                  background: 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #2563eb 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Solutions
              </span>
              <div
                className="absolute -bottom-4 left-0 w-28 h-1 rounded-full"
                style={{
                  background: 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #2563eb 100%)'
                }}
              ></div>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed max-w-2xl">
              Through years of forging intrinsic and enduring relationships with our clients, we have
              learned to cater for their needs and understand their DNA. We offer unwavering reliability
              through a set of bespoke and tailor-made solutions.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
